import React, { useEffect, useRef, useState } from "react";
import _ from "lodash";
import { Document, Page } from "react-pdf/dist/umd/entry.webpack";
import { Divider, Grid } from "semantic-ui-react";
import AnnotTypeMenu from "./annots/AnnotTypeMenu";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { addAnnotToCanvas } from "./annots/annotUtils";
import DocPrepareActionButtons from "./DocPrepareActionButtons";
import DocPrepareSignerList from "./signerList/DocPrepareSignerList";
import AnnotEditButtons from "./annots/AnnotEditButtons";
import AnnotDraggableField from "./annots/AnnotDraggableField";

import {
  createSigningParties,
  createSignerListFromAnnots,
  autoPlaceLinkedAgentClientSignatures,
} from "./annots/annotUtils";
import { roleIsOtherAgentsClients } from "./sendForSigning/sendForSigningUtils";
import { updateDocInDb } from "../../../../app/firestore/firestoreService";
import FormFieldSelector from "./formFields/FormFieldSelector";
import LoadingComponent from "../../../../app/layout/LoadingComponent";
import {
  addAnnot,
  changeSelectedSigner,
  selectDocAnnot,
  setSignerListDisplay,
  setSignerListPossible,
  fetchAnnots,
} from "../../../../app/annots/annotSlice";
import {
  createAllSuggestedAnnots,
  // createSuggestedAnnots,
  getAndSavePdfDimensions,
  getFormFieldValues,
} from "../../../../app/common/util/util";
import DocViewAgentAnnotField from "../docView/DocViewAgentAnnotField";
import DocViewSignedAnnotField from "../docView/DocViewSignedAnnotField";
import { getCurrentUserIpAddress } from "../../../profiles/profileSlice";
import { useMediaQuery } from "react-responsive";

const options = {
  cMapUrl: "cmaps/",
  cMapPacked: true,
  standardFontDataUrl: "standard_fonts/",
};

export function DocPrepareDashboard() {
  const isMobile = useMediaQuery({ query: "(max-width:1056px)" });

  const dispatch = useDispatch();
  const [numPages, setNumPages] = useState(null);
  const {
    annots,
    pageScalePrepare,
    activeAnnotType,
    selectedSigner,
    editMode,
    signerListDisplay,
  } = useSelector((state) => state.annot);
  const { doc, docUrl } = useSelector((state) => state.doc);
  const { currentUserProfile, currentUserIpAddress } = useSelector(
    (state) => state.profile
  );
  const { transClients, transaction, parties } = useSelector(
    (state) => state.transaction
  );
  const [formFieldValues, setFormFieldValues] = useState();
  const [updatedSuggestedAnnots, setUpdatedSuggestedAnnots] = useState(false);
  const [pageDimensions, setPageDimensions] = useState(
    doc.dimensions || {
      width: 612,
      height: 792,
    }
  );
  const pageScale = pageScalePrepare;
  const processedPartiesRef = useRef(new Set());

  useEffect(() => {
    if (currentUserProfile?.hasSigningVerification && !currentUserIpAddress)
      axios.get("https://api.ipify.org?format=json").then((res) => {
        dispatch(getCurrentUserIpAddress(res?.data?.ip));
      });
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    let partiesCopy = JSON.parse(JSON.stringify(parties));
    let transactionCopy = JSON.parse(JSON.stringify(transaction));
    const signingParties = createSigningParties(transactionCopy, partiesCopy);
    dispatch(
      setSignerListPossible(_.sortBy(signingParties.possibleSigners, "index"))
    );

    if (doc.signerListInProgress?.length > 0) {
      // Filter saved signer list to only include parties that still exist and have names
      const filteredSignerList = doc.signerListInProgress.filter((signer) => {
        // For agent's own clients, check if they have names
        const isAgentClient = transClients.some(
          (client) => client.role === signer.role
        );
        if (isAgentClient) {
          const client = transClients.find(
            (client) => client.role === signer.role
          );
          return client && client.firstName && client.lastName;
        }

        // For other parties, check if they exist in parties array and have names
        const party = parties.find((p) => p.role === signer.role);
        return party && party.firstName && party.lastName;
      });

      dispatch(setSignerListDisplay(filteredSignerList));
    } else {
      // Start with the default display signers (agent's own clients) but filter out those without names
      let displaySigners = signingParties.displaySigners.filter((signer) => {
        // For agent's own clients, only include if they have names
        const isAgentClient = transClients.some(
          (client) => client.role === signer.role
        );
        if (isAgentClient) {
          const client = transClients.find(
            (client) => client.role === signer.role
          );
          return client && client.firstName && client.lastName;
        }
        return true; // Keep non-client signers for now
      });

      // If document has annotations, add parties that have annotations to the display list
      if (doc.annotsInProgress && doc.annotsInProgress.length > 0) {
        const signerRolesWithAnnots = createSignerListFromAnnots(
          doc.annotsInProgress
        );

        // Add parties with annotations to display list if they're not already there
        signerRolesWithAnnots.forEach((role) => {
          if (!displaySigners.find((signer) => signer.role === role)) {
            const partyWithAnnots = signingParties.possibleSigners.find(
              (signer) => signer.role === role
            );
            if (partyWithAnnots) {
              // Only add if the party actually exists and has names
              const isAgentClient = transClients.some(
                (client) => client.role === role
              );
              if (isAgentClient) {
                const client = transClients.find(
                  (client) => client.role === role
                );
                if (client && client.firstName && client.lastName) {
                  displaySigners.push(partyWithAnnots);
                }
              } else {
                const party = parties.find((p) => p.role === role);
                if (party && party.firstName && party.lastName) {
                  displaySigners.push(partyWithAnnots);
                }
              }
            }
          }
        });
      }

      dispatch(setSignerListDisplay(_.sortBy(displaySigners, "index")));
    }

    if (
      doc.selectedSignerInProgress &&
      !_.isEmpty(doc.selectedSignerInProgress)
    ) {
      dispatch(changeSelectedSigner(doc.selectedSignerInProgress));
    } else {
      dispatch(changeSelectedSigner(transClients[0]));
    }
  }, [doc, dispatch, parties, transClients, transaction]);

  useEffect(() => {}, [pageScalePrepare, annots]);

  // Auto-place signatures for linked agents' clients when document is loaded with existing annotations
  useEffect(() => {
    if (
      doc &&
      doc.annotsInProgress &&
      doc.annotsInProgress.length > 0 &&
      parties.length > 0 &&
      transClients.length > 0 &&
      currentUserProfile
    ) {
      // Check if any annotations are not signed and could benefit from auto-placement
      const hasUnsignedAnnots = doc.annotsInProgress.some(
        (annot) =>
          !annot.signed &&
          !annot.agentsField &&
          (annot.type === "signature" || annot.type === "initials")
      );

      if (hasUnsignedAnnots) {
        const allParties = [...parties, ...transClients];
        autoPlaceLinkedAgentClientSignatures(
          doc.annotsInProgress,
          allParties,
          transaction,
          currentUserProfile
        )
          .then((autoPlacedAnnots) => {
            // Only update if signatures were actually placed
            const hasNewSignatures = autoPlacedAnnots.some(
              (annot) => annot.autoPlaced === true
            );

            if (hasNewSignatures) {
              dispatch(fetchAnnots(autoPlacedAnnots));
              updateDocInDb(
                doc.id,
                {
                  annotsInProgress: autoPlacedAnnots,
                },
                false
              );
            }
          })
          .catch((error) => {
            console.error(
              "Error auto-placing signatures for existing annotations:",
              error
            );
          });
      }
    }
  }, [doc, parties, transClients, currentUserProfile, transaction, dispatch]);

  useEffect(() => {
    getFormFieldValues(doc, transaction, setFormFieldValues);
  }, [doc, transaction]);

  useEffect(() => {
    if (
      doc &&
      doc.annotsInProgressSuggestedAdded === false &&
      updatedSuggestedAnnots === false &&
      !doc.shared
    ) {
      let suggestedAnnots = [];
      if (doc?.annotsInProgressSuggested) {
        suggestedAnnots = createAllSuggestedAnnots(transaction, doc, parties);
        setUpdatedSuggestedAnnots(true);

        // Auto-place signatures for linked agents' clients on initial load
        const allParties = [...parties, ...transClients];
        autoPlaceLinkedAgentClientSignatures(
          suggestedAnnots,
          allParties,
          transaction,
          currentUserProfile
        )
          .then((autoPlacedAnnots) => {
            updateDocInDb(
              doc.id,
              {
                annotsInProgress: autoPlacedAnnots,
                annotsInProgressSuggestedAdded: true,
              },
              false
            );
          })
          .catch((error) => {
            console.error(
              "Error auto-placing signatures on initial load:",
              error
            );
            // Fallback to original annotations if auto-placement fails
            updateDocInDb(
              doc.id,
              {
                annotsInProgress: suggestedAnnots,
                annotsInProgressSuggestedAdded: true,
              },
              false
            );
          });
      }
    }
  }, [
    doc,
    updatedSuggestedAnnots,
    transaction,
    parties,
    transClients,
    currentUserProfile,
  ]);

  // Handle adding annotations for newly added parties and removing annotations for deleted parties
  useEffect(() => {
    if (
      doc &&
      doc.annotsInProgressSuggestedAdded === true &&
      doc?.annotsInProgressSuggested &&
      !doc.shared &&
      parties.length > 0
    ) {
      // Get all current party roles including agent's own clients and other parties
      const currentPartyRoles = [
        ...parties.map((p) => p.role), // Other parties (Seller, Title Company, etc.)
        ...transClients.map((c) => c.role), // Agent's own clients (Buyer, Buyer 2, etc.)
      ];
      const currentAnnots = doc.annotsInProgress || [];

      // Initialize processedPartiesRef on first run with parties that already have annotations
      if (processedPartiesRef.current.size === 0) {
        const partiesWithAnnots = new Set();
        currentAnnots.forEach((annot) => {
          if (annot.signerRole && !annot.agentsField) {
            partiesWithAnnots.add(annot.signerRole);
          }
        });
        processedPartiesRef.current = partiesWithAnnots;
      }

      // Check for deleted parties (parties that were processed but no longer exist)
      const deletedPartyRoles = Array.from(processedPartiesRef.current).filter(
        (role) => !currentPartyRoles.includes(role)
      );

      // Check for new parties that haven't been processed
      const newPartyRoles = currentPartyRoles.filter(
        (role) => !processedPartiesRef.current.has(role)
      );

      let needsUpdate = false;
      let updatedAnnots = [...currentAnnots];

      // Remove annotations for deleted parties
      if (deletedPartyRoles.length > 0) {
        updatedAnnots = updatedAnnots.filter(
          (annot) => !deletedPartyRoles.includes(annot.signerRole)
        );
        // Remove deleted parties from processed set
        deletedPartyRoles.forEach((role) =>
          processedPartiesRef.current.delete(role)
        );
        needsUpdate = true;
      }

      // Add annotations for new parties
      if (newPartyRoles.length > 0) {
        // Get suggested annotations ONLY for the newly added parties
        const newPartyAnnots =
          doc.annotsInProgressSuggested?.filter((annot) =>
            newPartyRoles.includes(annot.signerRole)
          ) || [];

        if (newPartyAnnots.length > 0) {
          updatedAnnots = [...updatedAnnots, ...newPartyAnnots];
          needsUpdate = true;
        }

        // Mark these parties as processed
        newPartyRoles.forEach((role) => processedPartiesRef.current.add(role));
      }

      // Update document if changes were made
      if (needsUpdate) {
        // Combine all parties for auto-placement logic
        const allParties = [...parties, ...transClients];

        // Auto-place signatures for linked agents' clients
        autoPlaceLinkedAgentClientSignatures(
          updatedAnnots,
          allParties,
          transaction,
          currentUserProfile
        )
          .then((autoPlacedAnnots) => {
            // Update Redux state with new annotations (including auto-placed signatures)
            dispatch(fetchAnnots(autoPlacedAnnots));

            // Update document in database
            updateDocInDb(
              doc.id,
              {
                annotsInProgress: autoPlacedAnnots,
              },
              false
            );
          })
          .catch((error) => {
            console.error("Error auto-placing signatures:", error);
            // Fallback to original annotations if auto-placement fails
            dispatch(fetchAnnots(updatedAnnots));
            updateDocInDb(
              doc.id,
              {
                annotsInProgress: updatedAnnots,
              },
              false
            );
          });
      }
    }
  }, [doc, transaction, parties, transClients, dispatch, currentUserProfile]);

  async function onDocumentLoadSuccess(pdfObject) {
    getAndSavePdfDimensions(
      doc,
      pdfObject,
      setNumPages,
      setPageDimensions,
      "agent"
    );
  }

  function Canvas(props) {
    const canvasRef = useRef(null);
    useEffect(() => {
      const canvas = canvasRef.current;
      canvas.getContext("2d");
      canvas.ondrop = function (e) {
        e.preventDefault();
        const annot = addAnnotToCanvas(
          canvas,
          e,
          activeAnnotType,
          doc,
          selectedSigner,
          currentUserProfile,
          currentUserIpAddress,
          transaction,
          pageScale,
          editMode
        );
        dispatch(addAnnot(annot));
        dispatch(selectDocAnnot(annot));
      };
      canvas.ondragover = function (e) {
        e.stopPropagation();
        e.preventDefault();
      };
    }, []);
    return <canvas ref={canvasRef} {...props} />;
  }

  return (
    <div>
      <div
        // className="pdf-left-panel-wrapper large padding"
        className={
          isMobile
            ? "pdf-left-panel-wrapper-mobile small padding"
            : "pdf-left-panel-wrapper large padding"
        }
      >
        <Grid>
          <Grid.Column width={16}>
            <div className="large vertical margin">
              <p
                style={{
                  color: "#9a9a9a",
                  marginBottom: "3px",
                  marginTop: "30px",
                }}
              >
                Signer
              </p>
              <Divider className="zero top margin" />
              <DocPrepareSignerList />
            </div>
            <AnnotEditButtons />
          </Grid.Column>
        </Grid>
      </div>
      <div
        className={
          isMobile ? "pdf-annot-menu-wrapper-mobile" : "pdf-annot-menu-wrapper"
        }
        id="annot-menu"
      >
        <AnnotTypeMenu />
      </div>
      <div
        className={
          isMobile ? "pdf-prepare-wrapper-mobile" : "pdf-prepare-wrapper"
        }
        style={{
          backgroundColor: "#f3f6f8",
        }}
      >
        <div className="pdf-document-wrapper">
          <div
            style={{
              position: "relative",
            }}
          >
            <Document
              file={docUrl}
              onLoadSuccess={onDocumentLoadSuccess}
              loading={
                <div style={{ height: window.innerHeight }}>
                  <LoadingComponent />
                </div>
              }
              options={options}
            >
              {Array.from(new Array(numPages), (_, index) => (
                <div
                  className="pdf-prepare-page-wrapper large vertical padding"
                  key={`page-${index}`}
                  id={`page-${index}`}
                >
                  <div className="pdf-page-number">
                    Page {index + 1}/{numPages}
                  </div>
                  <div
                    className="pdf-page-container"
                    style={{
                      height: `${pageDimensions.height * pageScale}px`,
                      width: `${pageDimensions.width * pageScale}px`,
                    }}
                  >
                    <Page
                      scale={pageScale}
                      renderAnnotationLayer={false}
                      renderTextLayer={false}
                      key={`page_${index + 1}`}
                      pageNumber={index + 1}
                    />
                    <Canvas
                      className="pdf-canvas"
                      id={`canvas-${index}`}
                      style={{
                        width: `${pageDimensions.width * pageScale}px`,
                        height: `${pageDimensions.height * pageScale}px`,
                      }}
                    />
                    {numPages &&
                      formFieldValues?.map((formField) => (
                        <React.Fragment key={formField.name}>
                          {formField.page === index && (
                            <FormFieldSelector
                              formField={formField}
                              pageScale={pageScale}
                            />
                          )}
                        </React.Fragment>
                      ))}
                    {numPages &&
                      annots?.map((annot) => (
                        <React.Fragment key={annot.uniqueId}>
                          {annot.page === index &&
                            (!roleIsOtherAgentsClients(
                              annot.signerRole,
                              transaction
                            ) ||
                              annot.agentsField ||
                              signerListDisplay.find(
                                (signer) => signer.role === annot.signerRole
                              )) && (
                              <AnnotDraggableField
                                annot={annot}
                                pageScale={pageScale}
                                transaction={transaction}
                              />
                            )}
                        </React.Fragment>
                      ))}
                    {numPages &&
                      annots?.map((annot) => (
                        <React.Fragment key={`other-${annot.uniqueId}`}>
                          {annot.page === index &&
                            roleIsOtherAgentsClients(
                              annot.signerRole,
                              transaction
                            ) &&
                            !annot.agentsField &&
                            !signerListDisplay.find(
                              (signer) => signer.role === annot.signerRole
                            ) && (
                              <AnnotDraggableField
                                annot={annot}
                                pageScale={pageScale}
                                transaction={transaction}
                              />
                            )}
                        </React.Fragment>
                      ))}
                    {numPages &&
                      doc.pdfBurnVersion &&
                      doc.annotsByAgent &&
                      Object.entries(doc.annotsByAgent).map(([key, value]) => (
                        <React.Fragment key={key}>
                          {value.page === index && (
                            <DocViewAgentAnnotField
                              annot={value}
                              pageScale={pageScale}
                            />
                          )}
                        </React.Fragment>
                      ))}
                    {numPages &&
                      doc.pdfBurnVersion &&
                      doc.annotsToSign &&
                      Object.entries(doc.annotsToSign).map(([key, value]) => (
                        <React.Fragment key={key}>
                          {value.page === index && (
                            <>
                              {value.signed === true && (
                                <DocViewSignedAnnotField
                                  annot={value}
                                  pageScale={pageScale}
                                />
                              )}
                            </>
                          )}
                        </React.Fragment>
                      ))}
                  </div>
                </div>
              ))}
            </Document>
          </div>
        </div>
      </div>
      <Grid className="zero horizontal margin zero horizontal padding">
        <Grid.Column width={16} className="zero horizontal padding">
          <DocPrepareActionButtons />
        </Grid.Column>
      </Grid>
    </div>
  );
}
