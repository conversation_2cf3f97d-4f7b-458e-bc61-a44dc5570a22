// Mock all Firebase dependencies
jest.mock("../../../../../app/firestore/firebaseService", () => ({}));
jest.mock("../../../../../app/common/util/util", () => ({
  convertFullName: jest.fn(),
  convertInitials: jest.fn(),
}));
jest.mock("date-fns", () => ({
  format: jest.fn(() => "12/25/2023"),
}));

// Mock the firestore service
jest.mock("../../../../../app/firestore/firestoreService", () => ({
  fetchClientSignatureDataFromDb: jest.fn(),
}));

// Mock the sendForSigningUtils
jest.mock("../sendForSigning/sendForSigningUtils", () => ({
  roleIsOtherAgentsClients: jest.fn(),
}));

import { autoPlaceLinkedAgentClientSignatures } from "../annots/annotUtils";
import { fetchClientSignatureDataFromDb } from "../../../../../app/firestore/firestoreService";
import { roleIsOtherAgentsClients } from "../sendForSigning/sendForSigningUtils";

describe("autoPlaceLinkedAgentClientSignatures", () => {
  const mockCurrentUserProfile = {
    email: "<EMAIL>",
    firstName: "Test",
    lastName: "Agent",
  };

  const mockTransaction = {
    id: "trans123",
    agentRepresents: "Buyer",
  };

  const mockAllParties = [
    {
      id: "party1",
      role: "Buyer Agent",
      email: "<EMAIL>",
      isLinked: true,
      linkedTransactionId: "linkedTrans123",
    },
    {
      id: "party2",
      role: "Seller",
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Seller",
    },
  ];

  const mockAnnots = [
    {
      uniqueId: "annot1",
      type: "signature",
      signerRole: "Seller",
      signed: false,
      agentsField: false,
      x: 100,
      y: 200,
    },
    {
      uniqueId: "annot2",
      type: "initials",
      signerRole: "Seller",
      signed: false,
      agentsField: false,
      x: 150,
      y: 250,
    },
    {
      uniqueId: "annot3",
      type: "date",
      signerRole: "Seller",
      signed: false,
      agentsField: false,
      x: 200,
      y: 300,
    },
  ];

  const mockSignatureData = {
    signedFullName: "John Seller",
    signedInitials: "JS",
    signedFontFamily: "brush script mt, cursive",
    signingDetailsConfirmed: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset all mock implementations
    roleIsOtherAgentsClients.mockReset();
    fetchClientSignatureDataFromDb.mockReset();
  });

  it("should auto-place signatures for linked agent's clients when signature data is available", async () => {
    // Mock that the current user is a linked agent
    roleIsOtherAgentsClients.mockReturnValue(true);
    
    // Mock that signature data is found
    fetchClientSignatureDataFromDb.mockResolvedValue(mockSignatureData);

    const result = await autoPlaceLinkedAgentClientSignatures(
      mockAnnots,
      mockAllParties,
      mockTransaction,
      mockCurrentUserProfile
    );

    // Verify that fetchClientSignatureDataFromDb was called
    expect(fetchClientSignatureDataFromDb).toHaveBeenCalledWith(
      "<EMAIL>",
      "Seller"
    );

    // Verify that signatures were auto-placed
    const signatureAnnot = result.find(annot => annot.type === "signature");
    expect(signatureAnnot.signed).toBe(true);
    expect(signatureAnnot.signedFullName).toBe("John Seller");
    expect(signatureAnnot.signedInitials).toBe("JS");
    expect(signatureAnnot.autoPlaced).toBe(true);
    expect(signatureAnnot.text).toBe("John Seller");

    const initialsAnnot = result.find(annot => annot.type === "initials");
    expect(initialsAnnot.signed).toBe(true);
    expect(initialsAnnot.signedFullName).toBe("John Seller");
    expect(initialsAnnot.signedInitials).toBe("JS");
    expect(initialsAnnot.autoPlaced).toBe(true);
    expect(initialsAnnot.text).toBe("JS");

    const dateAnnot = result.find(annot => annot.type === "date");
    expect(dateAnnot.signed).toBe(true);
    expect(dateAnnot.autoPlaced).toBe(true);
    expect(dateAnnot.text).toMatch(/\d{2}\/\d{2}\/\d{4}/); // Date format
  });

  it("should not auto-place signatures when no signature data is available", async () => {
    roleIsOtherAgentsClients.mockReturnValue(true);
    fetchClientSignatureDataFromDb.mockResolvedValue(null);

    const result = await autoPlaceLinkedAgentClientSignatures(
      mockAnnots,
      mockAllParties,
      mockTransaction,
      mockCurrentUserProfile
    );

    // Verify that fetchClientSignatureDataFromDb was called
    expect(fetchClientSignatureDataFromDb).toHaveBeenCalledWith(
      "<EMAIL>",
      "Seller"
    );

    // Verify that signatures were not auto-placed
    const signatureAnnot = result.find(annot => annot.type === "signature");
    expect(signatureAnnot.signed).toBe(false);
    expect(signatureAnnot.autoPlaced).toBeUndefined();
  });

  it("should not auto-place signatures when user is not a linked agent", async () => {
    const nonLinkedParties = [
      {
        id: "party1",
        role: "Buyer Agent",
        email: "<EMAIL>",
        isLinked: false, // Not linked
      },
      {
        id: "party2",
        role: "Seller",
        email: "<EMAIL>",
        firstName: "John",
        lastName: "Seller",
      },
    ];

    // Reset mocks for this test
    roleIsOtherAgentsClients.mockClear();
    fetchClientSignatureDataFromDb.mockClear();

    const result = await autoPlaceLinkedAgentClientSignatures(
      mockAnnots,
      nonLinkedParties,
      mockTransaction,
      mockCurrentUserProfile
    );

    // Verify that fetchClientSignatureDataFromDb was not called
    expect(fetchClientSignatureDataFromDb).not.toHaveBeenCalled();

    // Verify that signatures were not auto-placed
    const signatureAnnot = result.find(annot => annot.type === "signature");
    expect(signatureAnnot.signed).toBe(false);
    expect(signatureAnnot.autoPlaced).toBeUndefined();
  });

  it("should handle errors gracefully", async () => {
    roleIsOtherAgentsClients.mockReturnValue(true);
    fetchClientSignatureDataFromDb.mockRejectedValue(new Error("Database error"));

    const result = await autoPlaceLinkedAgentClientSignatures(
      mockAnnots,
      mockAllParties,
      mockTransaction,
      mockCurrentUserProfile
    );

    // Should return original annotations without modification
    expect(result).toEqual(mockAnnots);
  });

  it("should return original annotations when required parameters are missing", async () => {
    const result = await autoPlaceLinkedAgentClientSignatures(
      null, // Missing annotations
      mockAllParties,
      mockTransaction,
      mockCurrentUserProfile
    );

    expect(result).toBeNull();
  });
});
